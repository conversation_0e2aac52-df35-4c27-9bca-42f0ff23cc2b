import argparse
import sys
from pathlib import Path

import streamlit.web.cli as stcli
from apscheduler.triggers.interval import IntervalTrigger

from jetbrains_refresh_token.constants import FRONTEND_APP_PATH
from jetbrains_refresh_token.daemon.scheduler_daemon import JetBrainsDaemon
from jetbrains_refresh_token.log_config import get_logger

logger = get_logger("main")


def launch_web_ui(port: int = 8501):
    """
    Launch the Streamlit Web UI.

    Args:
        port (int): Port number for the Web UI. Default is 8501.

    Returns:
        bool: Returns True if launched successfully, otherwise False.
    """
    logger.info("Launching Streamlit Web UI on port %d", port)

    sys.argv = [
        "streamlit",
        "run",
        str(FRONTEND_APP_PATH),
        "--server.port",
        str(port),
        "--server.headless",
        "true",
        "--browser.gatherUsageStats",
        "false",
    ]

    stcli.main()
    return True


def launch_scheduler(config_path: str = "config.json", test_mode: bool = False):
    """
    Launch the APScheduler background service.

    Args:
        config_path (str): Path to the configuration file
        test_mode (bool): Whether to run in test mode with faster execution

    Returns:
        bool: Returns True if launched successfully, otherwise False.
    """
    logger.info("Launching APScheduler background service...")

    try:
        # Add project root to Python path
        PROJECT_ROOT = Path(__file__).parent
        sys.path.insert(0, str(PROJECT_ROOT))

        # Import scheduler class

        # Create scheduler instance
        scheduler = JetBrainsDaemon(config_path=config_path)

        if test_mode:
            logger.info("Running scheduler in test mode...")
            # Import trigger for testing

            # Modify intervals for testing
            scheduler.scheduler.add_job(
                scheduler.token_refresh_job,
                trigger=IntervalTrigger(seconds=30),
                id='token_refresh_test',
                name='Token Refresh (Test)',
                replace_existing=True,
            )
            scheduler.scheduler.add_job(
                scheduler.quota_check_job,
                trigger=IntervalTrigger(minutes=1),
                id='quota_check_test',
                name='Quota Check (Test)',
                replace_existing=True,
            )

        # Start scheduler
        scheduler.start()
        return True

    except Exception as e:
        logger.error("Failed to launch APScheduler: %s", e)
        return False


def setup_argument_parser():
    parser = argparse.ArgumentParser(
        description='JetBrains JWT Token Refresh Tool',
        epilog='Usage example: python main.py --refresh-access OR python main.py --web',
    )

    parser.add_argument('--refresh-access', type=str, help='Refresh JWT for the specified account')

    parser.add_argument(
        '--refresh-all-access', action='store_true', help='Refresh JWT for all accounts'
    )

    parser.add_argument('--backup', action='store_true', help='Backup configuration file')
    parser.add_argument('--list', action='store_true', help='List all account information')
    parser.add_argument(
        '--export-jetbrainsai',
        type=str,
        nargs='?',
        const='jetbrainsai.json',
        help='Export configuration to jetbrainsai.json format (optionally specify output path)',
    )
    parser.add_argument(
        '--check-quota', action='store_true', help='Check quota remaining for all accounts'
    )
    parser.add_argument(
        '--force', action='store_true', help='Force update tokens (use with refresh options)'
    )

    # Web UI arguments
    parser.add_argument('--web', action='store_true', help='Launch Streamlit web interface')
    parser.add_argument(
        '--web-port', type=int, default=8501, help='Port for web interface (default: 8501)'
    )

    # Scheduler arguments
    parser.add_argument(
        '--scheduler', action='store_true', help='Launch APScheduler background service'
    )
    parser.add_argument(
        '--scheduler-test',
        action='store_true',
        help='Launch scheduler in test mode (fast execution)',
    )

    # Note: Combined service arguments are deprecated. Use Docker Compose instead.

    return parser


def main():
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Note: Combined services mode is deprecated. Use Docker Compose instead.
    if args.services:
        logger.error("Combined services mode is deprecated. Please use Docker Compose:")
        logger.error("  docker-compose up")
        return

    if args.scheduler or args.scheduler_test:
        config_path = args.config or "config.json"
        success = launch_scheduler(config_path, test_mode=args.scheduler_test)
        if success:
            logger.info("Scheduler launched successfully")
        else:
            logger.error("Failed to launch scheduler. Please check the logs.")
        return

    if args.web:
        success = launch_web_ui(args.web_port)
        if success:
            logger.info("Web UI launched successfully on port %d", args.web_port)
        else:
            logger.error("Failed to launch Web UI. Please check the logs.")
        return

    # Show help if no arguments provided
    if len(sys.argv) == 1:
        parser.print_help()
        return


# 主程序入口
if __name__ == "__main__":
    main()
