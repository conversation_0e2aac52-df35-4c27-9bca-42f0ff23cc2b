import argparse
import json
import os
import signal
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Union

from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED
from apscheduler.executors.pool import Thread<PERSON>oolExecutor
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger

from jetbrains_refresh_token.api.auth import (
    check_quota_remaining,
    refresh_expired_access_tokens,
)
from jetbrains_refresh_token.log_config import get_logger

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))


logger = get_logger("simple_scheduler")


class SimpleJetBrainsScheduler:
    """Simple APScheduler daemon for JetBrains token management."""

    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize the simple scheduler.

        Args:
            config_path: Path to the main configuration file
        """
        self.config_path = config_path
        self.status_file = Path("logs/scheduler_status.json")

        # Job execution history
        self.job_history: List[Dict] = []
        self.max_history = 50

        # Scheduler start time
        self.start_time = datetime.now()

        # 確保日誌目錄存在
        self._ensure_directories()

        # 初始化狀態文件
        self._init_status_file()

        # 使用簡單的記憶體存儲
        jobstores = {'default': MemoryJobStore()}

        # 配置執行器
        executors = {
            'default': ThreadPoolExecutor(max_workers=3),
        }

        # 作業默認設定
        job_defaults = {'coalesce': True, 'max_instances': 1, 'misfire_grace_time': 300}

        # 初始化排程器
        self.scheduler = BlockingScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Taipei',
        )

        # 添加事件監聽器
        self.scheduler.add_listener(self._job_executed_listener, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._job_error_listener, EVENT_JOB_ERROR)

        # 註冊信號處理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def _ensure_directories(self):
        """確保必要的目錄存在"""
        directories = ['logs']
        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    def _init_status_file(self):
        """初始化狀態文件"""
        initial_status = {
            "scheduler_status": "starting",
            "start_time": self.start_time.isoformat(),
            "last_update": datetime.now().isoformat(),
            "jobs": {},
            "job_history": [],
            "config_path": str(self.config_path) if self.config_path else "config.json",
            "health": {"status": "healthy", "last_check": datetime.now().isoformat()},
        }
        self._write_status(initial_status)

    def _write_status(self, status: Dict):
        """寫入狀態到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to write status file: {e}")

    def _job_executed_listener(self, event):
        """任務執行成功監聽器"""
        now = datetime.now()
        scheduled_time = event.scheduled_run_time

        # Handle timezone-aware datetime
        if scheduled_time.tzinfo is not None and now.tzinfo is None:
            scheduled_time = scheduled_time.replace(tzinfo=None)
        elif scheduled_time.tzinfo is None and now.tzinfo is not None:
            now = now.replace(tzinfo=None)

        job_info = {
            "job_id": event.job_id,
            "scheduled_time": event.scheduled_run_time.isoformat(),
            "execution_time": datetime.now().isoformat(),
            "status": "success",
            "duration": (now - scheduled_time).total_seconds(),
        }
        self._add_job_history(job_info)
        logger.info(f"Job {event.job_id} executed successfully")

    def _job_error_listener(self, event):
        """任務執行錯誤監聽器"""
        now = datetime.now()
        scheduled_time = event.scheduled_run_time

        # Handle timezone-aware datetime
        if scheduled_time.tzinfo is not None and now.tzinfo is None:
            scheduled_time = scheduled_time.replace(tzinfo=None)
        elif scheduled_time.tzinfo is None and now.tzinfo is not None:
            now = now.replace(tzinfo=None)

        job_info = {
            "job_id": event.job_id,
            "scheduled_time": event.scheduled_run_time.isoformat(),
            "execution_time": datetime.now().isoformat(),
            "status": "error",
            "error": str(event.exception),
            "duration": (now - scheduled_time).total_seconds(),
        }
        self._add_job_history(job_info)
        logger.error(f"Job {event.job_id} failed: {event.exception}")

    def _add_job_history(self, job_info: Dict):
        """添加任務歷史記錄"""
        self.job_history.append(job_info)
        # 保持歷史記錄在限制範圍內
        if len(self.job_history) > self.max_history:
            self.job_history = self.job_history[-self.max_history :]

    def _update_status(self, scheduler_status: str = "running"):
        """更新狀態文件"""
        try:
            jobs_info = {}
            for job in self.scheduler.get_jobs():
                next_run_time = getattr(job, 'next_run_time', None)
                jobs_info[job.id] = {
                    "name": job.name,
                    "next_run_time": next_run_time.isoformat() if next_run_time else None,
                    "trigger": str(job.trigger),
                    "max_instances": getattr(job, 'max_instances', 1),
                }

            status = {
                "scheduler_status": scheduler_status,
                "start_time": self.start_time.isoformat(),
                "last_update": datetime.now().isoformat(),
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                "jobs": jobs_info,
                "job_history": self.job_history[-10:],  # 最近10條記錄
                "config_path": str(self.config_path) if self.config_path else "config.json",
                "health": {
                    "status": "healthy" if scheduler_status == "running" else "unhealthy",
                    "last_check": datetime.now().isoformat(),
                },
            }
            self._write_status(status)
        except Exception as e:
            logger.error(f"Failed to update status: {e}")

    def signal_handler(self, signum, frame):
        """處理停止信號"""
        logger.info("收到停止信號: %s", signum)
        self._update_status("stopping")
        self.stop()
        sys.exit(0)

    def token_refresh_job(self):
        """Token 刷新任務"""
        try:
            logger.info("開始執行 token 刷新任務")
            # refresh_expired_access_tokens 不需要config_path參數，它會自動載入配置
            success = refresh_expired_access_tokens(is_forced=False)
            if success:
                logger.info("Token 刷新任務完成")
            else:
                logger.error("Token 刷新任務失敗")
            return success
        except Exception as e:
            logger.error("Token 刷新任務異常: %s", e)
            raise

    def quota_check_job(self):
        """Quota 檢查任務"""
        try:
            logger.info("開始執行 quota 檢查任務")
            # check_quota_remaining 不需要參數，它會自動載入配置
            success = check_quota_remaining()
            if success:
                logger.info("Quota 檢查任務完成")
            else:
                logger.error("Quota 檢查任務失敗")
            return success
        except Exception as e:
            logger.error("Quota 檢查任務異常: %s", e)
            raise

    def health_check_job(self):
        """健康檢查任務"""
        try:
            logger.debug("執行健康檢查")
            # 更新狀態文件
            self._update_status("running")
            logger.debug("健康檢查完成")
            return True
        except Exception as e:
            logger.error("健康檢查失敗: %s", e)
            raise

    def setup_jobs(self):
        """設定排程任務"""
        jobs_added = 0

        # Token 刷新 - 每30分鐘檢查一次
        self.scheduler.add_job(
            self.token_refresh_job,
            trigger=IntervalTrigger(minutes=30),
            id='token_refresh',
            name='JetBrains Token Refresh',
            replace_existing=True,
        )
        jobs_added += 1
        logger.info("Token 刷新任務已啟用 (每30分鐘)")

        # Quota 檢查 - 每2小時檢查一次
        self.scheduler.add_job(
            self.quota_check_job,
            trigger=IntervalTrigger(hours=2),
            id='quota_check',
            name='JetBrains Quota Check',
            replace_existing=True,
        )
        jobs_added += 1
        logger.info("Quota 檢查任務已啟用 (每2小時)")

        # 健康檢查 - 每5分鐘執行一次
        self.scheduler.add_job(
            self.health_check_job,
            trigger=IntervalTrigger(minutes=5),
            id='health_check',
            name='Scheduler Health Check',
            replace_existing=True,
        )
        jobs_added += 1
        logger.info("健康檢查任務已啟用 (每5分鐘)")

        logger.info("已設定 %d 個排程任務", jobs_added)

    def start(self):
        """啟動排程器"""
        logger.info("正在啟動 JetBrains 簡單排程器...")

        # 檢查配置文件
        config_path = Path(self.config_path) if self.config_path else Path("config.json")
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_path}")

        self.setup_jobs()
        self._update_status("running")

        # 顯示已排程的任務
        jobs = self.scheduler.get_jobs()
        logger.info("已載入 %d 個任務:", len(jobs))
        for job in jobs:
            next_run = getattr(job, 'next_run_time', '未知')
            logger.info("  - %s (%s): 下次執行 %s", job.name, job.id, next_run)

        try:
            logger.info("排程器啟動成功，開始執行任務...")
            self.scheduler.start()
        except KeyboardInterrupt:
            logger.info("收到鍵盤中斷信號")
            self.stop()

    def stop(self):
        """停止排程器"""
        logger.info("正在停止排程器...")
        self._update_status("stopped")
        if self.scheduler.running:
            self.scheduler.shutdown(wait=True)
        logger.info("排程器已停止")


def main():
    """主程式"""
    parser = argparse.ArgumentParser(description="JetBrains Simple Token Scheduler")
    parser.add_argument("--config", help="Path to config file", default="config.json")
    parser.add_argument(
        "--test", action="store_true", help="Run in test mode with faster intervals"
    )
    args = parser.parse_args()

    logger.info("啟動 JetBrains 簡單排程器服務")

    # 創建並啟動排程器
    scheduler = SimpleJetBrainsScheduler(config_path=args.config)

    if args.test:
        logger.info("運行在測試模式...")
        # 在測試模式下使用更短的間隔
        scheduler.scheduler.add_job(
            scheduler.token_refresh_job,
            trigger=IntervalTrigger(seconds=30),
            id='token_refresh_test',
            name='Token Refresh (Test)',
            replace_existing=True,
        )
        scheduler.scheduler.add_job(
            scheduler.quota_check_job,
            trigger=IntervalTrigger(minutes=1),
            id='quota_check_test',
            name='Quota Check (Test)',
            replace_existing=True,
        )

    scheduler.start()


if __name__ == "__main__":
    main()
