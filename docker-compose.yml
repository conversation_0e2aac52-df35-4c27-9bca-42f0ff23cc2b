services:
  # JetBrains Scheduler Daemon - 獨立背景服務
  jetbrains-daemon:
    build:
      context: .
      dockerfile: Dockerfile.daemon
    container_name: jetbrains-daemon
    volumes:
      - ./config.json:/app/config.json:ro
      - ./jetbrainsai.json:/app/jetbrainsai.json
      - shared-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "test", "-f", "/app/logs/scheduler_status.json"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      - DAEMON_MODE=true

  # JetBrains Frontend - Streamlit Web UI
  jetbrains-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: jetbrains-frontend
    ports:
      - "8501:8501"
    volumes:
      - ./config.json:/app/config.json:ro
      - ./jetbrainsai.json:/app/jetbrainsai.json:ro
      - shared-logs:/app/logs:ro
    restart: unless-stopped
    depends_on:
      - jetbrains-daemon
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      - FRONTEND_MODE=true

  # JetBrains AI to API 服務 - 獨立服務，只共用 jetbrainsai.json
  # jetbrainsai2api:
  #   build: .
  #   container_name: jetbrainsai2api
  #   ports:
  #     - "8000:8000"
  #   volumes:
  #     - ./jetbrainsai.json:/app/jetbrainsai.json:ro
  #     - ./client_api_keys.json:/app/client_api_keys.json:ro
  #     - ./models.json:/app/models.json:ro
  #   environment:
  #     - DEBUG_MODE=${DEBUG_MODE:-false}
  #   restart: unless-stopped

volumes:
  shared-logs:
    driver: local

networks:
  default:
    name: jetbrains-network
