#!/usr/bin/env python3
"""
JetBrains Scheduler Entry Point

This script provides a standalone entry point for the JetBrains scheduler.
It uses the enhanced JetBrainsDaemon implementation.

Usage:
    python jetbrains_scheduler.py [--config CONFIG_PATH] [--test]

Examples:
    # Run with default config
    python jetbrains_scheduler.py

    # Run with custom config
    python jetbrains_scheduler.py --config /path/to/config.json

    # Run in test mode (faster intervals)
    python jetbrains_scheduler.py --test
"""

import sys
from pathlib import Path

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from jetbrains_refresh_token.daemon.scheduler_daemon import main

if __name__ == "__main__":
    main()
