# 使用官方 Python 3.12 slim 映像作為基礎映像
FROM python:3.12-slim

# 設定工作目錄
WORKDIR /app

# 設定環境變數
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DAEMON_MODE=true

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴文件
COPY requirements.txt pyproject.toml ./

# 安裝 Python 依賴
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# 複製應用程式代碼
COPY . .

# 創建必要的目錄
RUN mkdir -p logs

# 設定權限
RUN chmod +x jetbrains_scheduler.py

# 健康檢查 - 檢查狀態文件是否存在且最近更新
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD test -f /app/logs/scheduler_status.json && \
        test $(find /app/logs/scheduler_status.json -mmin -2 | wc -l) -gt 0 || exit 1

# 預設命令：啟動 Daemon
CMD ["python", "jetbrains_scheduler.py", "--config", "config.json"]
